export default {
  expo: {
    name: "HVPPYPlug+ Vendor",
    slug: "hvppyplug-vendor",
    version: "1.0.0",
    orientation: "portrait",
    icon: "./assets/icon.png",
    userInterfaceStyle: "light",
    newArchEnabled: true,
    splash: {
      image: "./assets/splash-icon.png",
      resizeMode: "contain",
      backgroundColor: "#ffffff"
    },
    ios: {
      supportsTablet: true
    },
    android: {
      adaptiveIcon: {
        foregroundImage: "./assets/adaptive-icon.png",
        backgroundColor: "#ffffff"
      },
      edgeToEdgeEnabled: true
    },
    web: {
      favicon: "./assets/favicon.png"
    },
    extra: {
      // Appwrite Configuration
      appwriteEndpoint:
        process.env.EXPO_PUBLIC_APPWRITE_ENDPOINT ||
        "https://cloud.appwrite.io/v1",
      appwriteProjectId: process.env.EXPO_PUBLIC_APPWRITE_PROJECT_ID || "",
      appwriteDatabaseId:
        process.env.EXPO_PUBLIC_APPWRITE_DATABASE_ID || "hvppyplug-main",
      appwriteStorageId:
        process.env.EXPO_PUBLIC_APPWRITE_STORAGE_ID || "images",

      // Environment
      environment: process.env.NODE_ENV || "development",

      // Feature Flags
      enablePushNotifications:
        process.env.EXPO_PUBLIC_ENABLE_PUSH_NOTIFICATIONS === "true",
      enableLocationTracking:
        process.env.EXPO_PUBLIC_ENABLE_LOCATION_TRACKING === "true",
      enableOfflineMode: process.env.EXPO_PUBLIC_ENABLE_OFFLINE_MODE === "true",
    }
  }
};
