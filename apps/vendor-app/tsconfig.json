{
  "compilerOptions": {
    "allowJs": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "isolatedModules": true,
    "jsx": "react-jsx",
    "lib": [
      "es2017"
    ],
    "moduleResolution": "node",
    "noEmit": true,
    "strict": true,
    "target": "esnext",
    "resolveJsonModule": true,
    "skipLibCheck": true,
    "skipDefaultLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@hvppyplug/common": ["../../packages/common/src"],
      "@hvppyplug/compound-components": ["../../packages/compound-components/src"],
      "@hvppyplug/ui-components-v2": ["../../packages/ui-components-v2/src"],
      "@hvppyplug/mobile-services": ["../../packages/mobile-services/src"]
    }
  },
  "include": [
    "**/*.ts",
    "**/*.tsx"
  ],
  "exclude": [
    "node_modules"
  ],

}
