import React from 'react'
import {
  View,
  Text,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  StyleSheet,
  ViewStyle,
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { useTheme } from '../../hooks/useTheme'

export interface FormContainerProps {
  children: React.ReactNode
  scrollable?: boolean
  keyboardAvoiding?: boolean
  safeArea?: boolean
  padding?: boolean
  style?: ViewStyle
  contentContainerStyle?: ViewStyle
}

export function FormContainer({
  children,
  scrollable = true,
  keyboardAvoiding = true,
  safeArea = true,
  padding = true,
  style,
  contentContainerStyle,
}: FormContainerProps) {
  const { colors, spacing } = useTheme()

  const containerStyle = [
    styles.container,
    {
      backgroundColor: colors.background.primary,
    },
    style,
  ]

  const contentStyle = [
    padding && {
      paddingHorizontal: spacing[6],
      paddingVertical: spacing[4],
    },
    contentContainerStyle,
  ]

  const renderContent = () => {
    if (scrollable) {
      return (
        <ScrollView
          style={containerStyle}
          contentContainerStyle={[styles.scrollContent, contentStyle]}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          {children}
        </ScrollView>
      )
    }

    return (
      <View style={[containerStyle, contentStyle]}>
        {children}
      </View>
    )
  }

  const content = keyboardAvoiding ? (
    <KeyboardAvoidingView
      style={styles.keyboardAvoid}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
    >
      {renderContent()}
    </KeyboardAvoidingView>
  ) : (
    renderContent()
  )

  return safeArea ? (
    <SafeAreaView style={styles.safeArea}>
      {content}
    </SafeAreaView>
  ) : (
    content
  )
}



// Form Section Component
export interface FormSectionProps {
  children: React.ReactNode
  title?: string
  subtitle?: string
  spacing?: 'sm' | 'md' | 'lg'
  style?: ViewStyle
}

export function FormSection({
  children,
  title,
  subtitle,
  spacing: sectionSpacing = 'md',
  style,
}: FormSectionProps) {
  const { colors, typography, spacing } = useTheme()

  const getSectionSpacing = () => {
    switch (sectionSpacing) {
      case 'sm': return spacing[4]
      case 'lg': return spacing[8]
      default: return spacing[6]
    }
  }

  return (
    <View
      style={[
        {
          marginBottom: getSectionSpacing(),
        },
        style,
      ]}
    >
      {title && (
        <Text
          style={[
            typography.heading.h3,
            {
              color: colors.text.primary,
              marginBottom: subtitle ? spacing[1] : spacing[4],
            },
          ]}
        >
          {title}
        </Text>
      )}
      {subtitle && (
        <Text
          style={[
            typography.body.medium,
            {
              color: colors.text.secondary,
              marginBottom: spacing[4],
            },
          ]}
        >
          {subtitle}
        </Text>
      )}
      {children}
    </View>
  )
}

// Form Actions Component
export interface FormActionsProps {
  children: React.ReactNode
  direction?: 'row' | 'column'
  spacing?: 'sm' | 'md' | 'lg'
  align?: 'start' | 'center' | 'end' | 'stretch'
  style?: ViewStyle
}

export function FormActions({
  children,
  direction = 'column',
  spacing: actionSpacing = 'md',
  align = 'stretch',
  style,
}: FormActionsProps) {
  const { spacing } = useTheme()

  const getActionSpacing = () => {
    switch (actionSpacing) {
      case 'sm': return spacing[2]
      case 'lg': return spacing[6]
      default: return spacing[4]
    }
  }

  const getAlignItems = () => {
    switch (align) {
      case 'start': return 'flex-start'
      case 'center': return 'center'
      case 'end': return 'flex-end'
      case 'stretch': return 'stretch'
      default: return 'stretch'
    }
  }

  return (
    <View
      style={[
        {
          flexDirection: direction,
          alignItems: getAlignItems(),
          gap: getActionSpacing(),
          marginTop: spacing[6],
        },
        style,
      ]}
    >
      {children}
    </View>
  )
}

// Form Divider Component
export interface FormDividerProps {
  text?: string
  style?: ViewStyle
}

export function FormDivider({ text, style }: FormDividerProps) {
  const { colors, typography, spacing } = useTheme()

  if (text) {
    return (
      <View
        style={[
          styles.dividerContainer,
          {
            marginVertical: spacing[6],
          },
          style,
        ]}
      >
        <View
          style={[
            styles.dividerLine,
            { backgroundColor: colors.border.primary },
          ]}
        />
        <Text
          style={[
            typography.body.small,
            {
              color: colors.text.secondary,
              backgroundColor: colors.background.primary,
              paddingHorizontal: spacing[3],
            },
          ]}
        >
          {text}
        </Text>
        <View
          style={[
            styles.dividerLine,
            { backgroundColor: colors.border.primary },
          ]}
        />
      </View>
    )
  }

  return (
    <View
      style={[
        {
          height: 1,
          backgroundColor: colors.border.primary,
          marginVertical: spacing[6],
        },
        style,
      ]}
    />
  )
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  container: {
    flex: 1,
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dividerLine: {
    flex: 1,
    height: 1,
  },
})
