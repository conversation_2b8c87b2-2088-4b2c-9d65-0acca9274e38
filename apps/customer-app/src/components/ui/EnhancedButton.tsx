import React from 'react'
import {
  TouchableOpacity,
  Text,
  View,
  ActivityIndicator,
  StyleSheet,
  ViewStyle,
  TextStyle,
  TouchableOpacityProps,
} from 'react-native'
import { useTheme } from '../../hooks/useTheme'

export interface EnhancedButtonProps extends Omit<TouchableOpacityProps, 'style'> {
  title: string
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'
  size?: 'sm' | 'md' | 'lg'
  loading?: boolean
  disabled?: boolean
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  fullWidth?: boolean
  style?: ViewStyle
  textStyle?: TextStyle
  loadingText?: string
}

export function EnhancedButton({
  title,
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled = false,
  leftIcon,
  rightIcon,
  fullWidth = false,
  style,
  textStyle,
  loadingText,
  onPress,
  ...props
}: EnhancedButtonProps) {
  const { colors, typography, borderRadius, spacing } = useTheme()

  const isDisabled = disabled || loading

  const getButtonHeight = () => {
    switch (size) {
      case 'sm': return 36
      case 'lg': return 56
      default: return 48
    }
  }

  const getButtonPadding = () => {
    switch (size) {
      case 'sm': return spacing[3]
      case 'lg': return spacing[6]
      default: return spacing[4]
    }
  }

  const getTextStyle = () => {
    switch (size) {
      case 'sm': return typography.label.small
      case 'lg': return typography.label.large
      default: return typography.label.medium
    }
  }

  const getIconSize = () => {
    switch (size) {
      case 'sm': return 16
      case 'lg': return 24
      default: return 20
    }
  }

  const getButtonStyles = () => {
    const baseStyle = {
      height: getButtonHeight(),
      paddingHorizontal: getButtonPadding(),
      borderRadius: borderRadius.md,
      borderWidth: 1,
    }

    switch (variant) {
      case 'primary':
        return {
          ...baseStyle,
          backgroundColor: isDisabled ? colors.gray[300] : colors.primary[500],
          borderColor: isDisabled ? colors.gray[300] : colors.primary[500],
        }
      case 'secondary':
        return {
          ...baseStyle,
          backgroundColor: isDisabled ? colors.gray[100] : colors.secondary[500],
          borderColor: isDisabled ? colors.gray[300] : colors.secondary[500],
        }
      case 'outline':
        return {
          ...baseStyle,
          backgroundColor: colors.transparent,
          borderColor: isDisabled ? colors.gray[300] : colors.primary[500],
        }
      case 'ghost':
        return {
          ...baseStyle,
          backgroundColor: colors.transparent,
          borderColor: colors.transparent,
        }
      case 'danger':
        return {
          ...baseStyle,
          backgroundColor: isDisabled ? colors.gray[300] : colors.error[500],
          borderColor: isDisabled ? colors.gray[300] : colors.error[500],
        }
      default:
        return baseStyle
    }
  }

  const getTextColor = () => {
    if (isDisabled) {
      return variant === 'outline' || variant === 'ghost' ? colors.gray[400] : colors.gray[500]
    }

    switch (variant) {
      case 'primary':
      case 'secondary':
      case 'danger':
        return colors.white
      case 'outline':
        return colors.primary[500]
      case 'ghost':
        return colors.primary[500]
      default:
        return colors.white
    }
  }

  const getLoadingColor = () => {
    switch (variant) {
      case 'primary':
      case 'secondary':
      case 'danger':
        return colors.white
      case 'outline':
      case 'ghost':
        return colors.primary[500]
      default:
        return colors.white
    }
  }

  const handlePress = (event: any) => {
    if (!isDisabled && onPress) {
      onPress(event)
    }
  }

  return (
    <TouchableOpacity
      {...props}
      onPress={handlePress}
      disabled={isDisabled}
      style={[
        styles.button,
        getButtonStyles(),
        fullWidth && styles.fullWidth,
        isDisabled && styles.disabled,
        style,
      ]}
      activeOpacity={isDisabled ? 1 : 0.8}
    >
      <View style={styles.content}>
        {loading ? (
          <>
            <ActivityIndicator
              size="small"
              color={getLoadingColor()}
              style={styles.loadingIndicator}
            />
            {loadingText && (
              <Text
                style={[
                  getTextStyle(),
                  { color: getTextColor() },
                  textStyle,
                ]}
              >
                {loadingText}
              </Text>
            )}
          </>
        ) : (
          <>
            {leftIcon && (
              <View style={styles.leftIcon}>
                {leftIcon}
              </View>
            )}
            <Text
              style={[
                getTextStyle(),
                { color: getTextColor() },
                textStyle,
              ]}
            >
              {title}
            </Text>
            {rightIcon && (
              <View style={styles.rightIcon}>
                {rightIcon}
              </View>
            )}
          </>
        )}
      </View>
    </TouchableOpacity>
  )
}

const styles = StyleSheet.create({
  button: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullWidth: {
    width: '100%',
  },
  disabled: {
    opacity: 0.6,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  leftIcon: {
    marginRight: 8,
  },
  rightIcon: {
    marginLeft: 8,
  },
  loadingIndicator: {
    marginRight: 8,
  },
})

// Social Button Component
export interface SocialButtonProps extends Omit<EnhancedButtonProps, 'variant' | 'leftIcon'> {
  provider: 'google' | 'apple' | 'facebook'
  iconColor?: string
}

export function SocialButton({
  provider,
  iconColor,
  ...props
}: SocialButtonProps) {
  const { colors } = useTheme()

  const getProviderConfig = () => {
    switch (provider) {
      case 'google':
        return {
          title: 'Continue with Google',
          backgroundColor: colors.white,
          textColor: colors.gray[700],
          borderColor: colors.gray[300],
          icon: '🔍', // In a real app, use proper Google icon
        }
      case 'apple':
        return {
          title: 'Continue with Apple',
          backgroundColor: colors.black,
          textColor: colors.white,
          borderColor: colors.black,
          icon: '🍎', // In a real app, use proper Apple icon
        }
      case 'facebook':
        return {
          title: 'Continue with Facebook',
          backgroundColor: '#1877F2',
          textColor: colors.white,
          borderColor: '#1877F2',
          icon: '📘', // In a real app, use proper Facebook icon
        }
      default:
        return {
          title: 'Continue',
          backgroundColor: colors.gray[100],
          textColor: colors.gray[700],
          borderColor: colors.gray[300],
          icon: '🔗',
        }
    }
  }

  const config = getProviderConfig()

  return (
    <EnhancedButton
      {...props}
      title={config.title}
      variant="outline"
      leftIcon={
        <Text style={{ fontSize: 20 }}>
          {config.icon}
        </Text>
      }
      style={[
        {
          backgroundColor: config.backgroundColor,
          borderColor: config.borderColor,
        },
        props.style,
      ]}
      textStyle={[
        { color: config.textColor },
        props.textStyle,
      ]}
    />
  )
}
