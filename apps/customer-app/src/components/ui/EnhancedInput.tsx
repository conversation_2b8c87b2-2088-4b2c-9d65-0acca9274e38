import React, { useState, useRef, useEffect } from 'react'
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Animated,
  StyleSheet,
  TextInputProps,
  ViewStyle,
  TextStyle,
} from 'react-native'
import { EyeIcon, EyeOffIcon, CheckCircleIcon, XCircleIcon, AlertCircleIcon } from 'lucide-react-native'
import { useTheme } from '../../hooks/useTheme'

export interface EnhancedInputProps extends Omit<TextInputProps, 'style'> {
  label: string
  error?: string
  success?: boolean
  helperText?: string
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  isPassword?: boolean
  showPasswordToggle?: boolean
  containerStyle?: ViewStyle
  inputStyle?: TextStyle
  labelStyle?: TextStyle
  required?: boolean
  disabled?: boolean
  variant?: 'default' | 'filled' | 'outlined'
  size?: 'sm' | 'md' | 'lg'
}

export function EnhancedInput({
  label,
  error,
  success,
  helperText,
  leftIcon,
  rightIcon,
  isPassword = false,
  showPasswordToggle = false,
  containerStyle,
  inputStyle,
  labelStyle,
  required = false,
  disabled = false,
  variant = 'outlined',
  size = 'md',
  value,
  onFocus,
  onBlur,
  ...props
}: EnhancedInputProps) {
  const { colors, typography, borderRadius, spacing } = useTheme()
  const [isFocused, setIsFocused] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const labelAnimation = useRef(new Animated.Value(value ? 1 : 0)).current
  const borderAnimation = useRef(new Animated.Value(0)).current

  const hasError = !!error
  const hasSuccess = success && !hasError
  const hasValue = !!value

  useEffect(() => {
    Animated.timing(labelAnimation, {
      toValue: isFocused || hasValue ? 1 : 0,
      duration: 200,
      useNativeDriver: false,
    }).start()
  }, [isFocused, hasValue, labelAnimation])

  useEffect(() => {
    Animated.timing(borderAnimation, {
      toValue: isFocused ? 1 : 0,
      duration: 200,
      useNativeDriver: false,
    }).start()
  }, [isFocused, borderAnimation])

  const handleFocus = (e: any) => {
    setIsFocused(true)
    onFocus?.(e)
  }

  const handleBlur = (e: any) => {
    setIsFocused(false)
    onBlur?.(e)
  }

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword)
  }

  const getBorderColor = () => {
    if (hasError) return colors.error[500]
    if (hasSuccess) return colors.success[500]
    if (isFocused) return colors.primary[500]
    return colors.border.primary
  }

  const getInputHeight = () => {
    switch (size) {
      case 'sm': return 40
      case 'lg': return 56
      default: return 48
    }
  }

  const getLabelSize = () => {
    switch (size) {
      case 'sm': return typography.body.small
      case 'lg': return typography.body.large
      default: return typography.body.medium
    }
  }

  const getStatusIcon = () => {
    if (hasError) {
      return <XCircleIcon size={20} color={colors.error[500]} />
    }
    if (hasSuccess) {
      return <CheckCircleIcon size={20} color={colors.success[500]} />
    }
    return null
  }

  const labelTop = labelAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [getInputHeight() / 2 - 8, -8],
  })

  const labelScale = labelAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [1, 0.85],
  })

  const borderWidth = borderAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [1, 2],
  })

  return (
    <View style={[styles.container, containerStyle]}>
      {/* Input Container */}
      <Animated.View
        style={[
          styles.inputContainer,
          {
            height: getInputHeight(),
            borderRadius: borderRadius.md,
            borderColor: getBorderColor(),
            borderWidth,
            backgroundColor: variant === 'filled' ? colors.background.secondary : colors.background.primary,
            opacity: disabled ? 0.6 : 1,
          },
        ]}
      >
        {/* Left Icon */}
        {leftIcon && (
          <View style={styles.leftIconContainer}>
            {leftIcon}
          </View>
        )}

        {/* Input Field */}
        <TextInput
          {...props}
          value={value}
          onFocus={handleFocus}
          onBlur={handleBlur}
          secureTextEntry={isPassword && !showPassword}
          editable={!disabled}
          style={[
            styles.input,
            getLabelSize(),
            {
              color: colors.text.primary,
              paddingLeft: leftIcon ? 0 : spacing[4],
              paddingRight: (rightIcon || showPasswordToggle || getStatusIcon()) ? 0 : spacing[4],
            },
            inputStyle,
          ]}
          placeholderTextColor={colors.text.tertiary}
        />

        {/* Floating Label */}
        <Animated.View
          style={[
            styles.labelContainer,
            {
              top: labelTop,
              left: leftIcon ? 44 : spacing[4],
              transform: [{ scale: labelScale }],
            },
          ]}
          pointerEvents="none"
        >
          <View style={[
            styles.labelBackground,
            { backgroundColor: variant === 'filled' ? colors.background.secondary : colors.background.primary }
          ]}>
            <Text
              style={[
                getLabelSize(),
                {
                  color: hasError ? colors.error[500] : isFocused ? colors.primary[500] : colors.text.secondary,
                },
                labelStyle,
              ]}
            >
              {label}
              {required && <Text style={{ color: colors.error[500] }}> *</Text>}
            </Text>
          </View>
        </Animated.View>

        {/* Right Icons */}
        <View style={styles.rightIconsContainer}>
          {getStatusIcon()}
          {showPasswordToggle && isPassword && (
            <TouchableOpacity
              onPress={togglePasswordVisibility}
              style={styles.passwordToggle}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              {showPassword ? (
                <EyeOffIcon size={20} color={colors.text.secondary} />
              ) : (
                <EyeIcon size={20} color={colors.text.secondary} />
              )}
            </TouchableOpacity>
          )}
          {rightIcon && !getStatusIcon() && (
            <View style={styles.rightIcon}>
              {rightIcon}
            </View>
          )}
        </View>
      </Animated.View>

      {/* Helper Text / Error Message */}
      {(error || helperText) && (
        <View style={styles.helperContainer}>
          {hasError && <AlertCircleIcon size={16} color={colors.error[500]} />}
          <Text
            style={[
              typography.caption.small,
              {
                color: hasError ? colors.error[500] : colors.text.secondary,
                marginLeft: hasError ? spacing[1] : 0,
              },
            ]}
          >
            {error || helperText}
          </Text>
        </View>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  input: {
    flex: 1,
    paddingVertical: 0,
    margin: 0,
  },
  labelContainer: {
    position: 'absolute',
    paddingHorizontal: 4,
  },
  labelBackground: {
    paddingHorizontal: 4,
  },
  leftIconContainer: {
    paddingLeft: 12,
    paddingRight: 8,
  },
  rightIconsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: 12,
    gap: 8,
  },
  passwordToggle: {
    padding: 4,
  },
  rightIcon: {
    padding: 4,
  },
  helperContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 6,
    paddingHorizontal: 4,
  },
})
