import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { account } from '../lib/appwrite'

// Types
export interface User {
  id: string
  email: string
  name: string
  phone?: string
  avatar?: string
  addresses: Address[]
  preferences: UserPreferences
  createdAt: string
  updatedAt: string
}

export interface Address {
  id: string
  label: string
  street: string
  city: string
  province: string
  postalCode: string
  country: string
  coordinates?: {
    latitude: number
    longitude: number
  }
  isDefault: boolean
}

export interface UserPreferences {
  notifications: {
    push: boolean
    email: boolean
    sms: boolean
    orderUpdates: boolean
    promotions: boolean
  }
  location: {
    shareLocation: boolean
    backgroundLocation: boolean
  }
  language: string
  currency: string
}

export interface AuthState {
  // State
  user: User | null
  isAuthenticated: boolean
  hasCompletedOnboarding: boolean
  isLoading: boolean
  error: string | null

  // Actions
  login: (email: string, password: string) => Promise<void>
  register: (email: string, password: string, name: string) => Promise<void>
  logout: () => Promise<void>
  forgotPassword: (email: string) => Promise<void>
  resetPassword: (token: string, newPassword: string) => Promise<void>
  verifyOTP: (email: string, otp: string) => Promise<void>
  updateProfile: (updates: Partial<User>) => Promise<void>
  addAddress: (address: Omit<Address, 'id'>) => Promise<void>
  updateAddress: (addressId: string, updates: Partial<Address>) => Promise<void>
  deleteAddress: (addressId: string) => Promise<void>
  setDefaultAddress: (addressId: string) => Promise<void>
  updatePreferences: (preferences: Partial<UserPreferences>) => Promise<void>
  completeOnboarding: () => void
  clearError: () => void
  refreshUser: () => Promise<void>
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial State
      user: null,
      isAuthenticated: false,
      hasCompletedOnboarding: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (email: string, password: string) => {
        set({ isLoading: true, error: null })
        try {
          const session = await account.createEmailPasswordSession(email, password)

          if (session) {
            const user = await account.get()
            set({
              user: {
                id: user.$id,
                email: user.email,
                name: user.name,
                phone: user.phone,
                addresses: [],
                preferences: {
                  notifications: {
                    push: true,
                    email: true,
                    sms: false,
                    orderUpdates: true,
                    promotions: false,
                  },
                  location: {
                    shareLocation: true,
                    backgroundLocation: false,
                  },
                  language: 'en',
                  currency: 'ZAR',
                },
                createdAt: user.$createdAt,
                updatedAt: user.$updatedAt,
              },
              isAuthenticated: true,
              isLoading: false,
            })
          }
        } catch (error: any) {
          let errorMessage = 'Login failed'

          if (error?.message?.includes('Service is initializing')) {
            errorMessage = 'Service is starting up. Please wait a moment and try again.'
          } else {
            errorMessage = error?.message || 'Login failed'
          }

          set({
            error: errorMessage,
            isLoading: false,
          })
          throw error
        }
      },

      register: async (email: string, password: string, name: string) => {
        set({ isLoading: true, error: null })
        try {
          const user = await account.create('unique()', email, password, name)
          
          if (user) {
            // Auto-login after registration
            await get().login(email, password)
          }
        } catch (error: any) {
          set({
            error: error.message || 'Registration failed',
            isLoading: false,
          })
          throw error
        }
      },

      logout: async () => {
        set({ isLoading: true })
        try {
          await account.deleteSession('current')
          
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          })
        } catch (error: any) {
          set({
            error: error.message || 'Logout failed',
            isLoading: false,
          })
        }
      },

      forgotPassword: async (email: string) => {
        set({ isLoading: true, error: null })
        try {
          await account.createRecovery(email, 'https://hvppyplug.com/reset-password')
          set({ isLoading: false })
        } catch (error: any) {
          set({
            error: error.message || 'Failed to send reset email',
            isLoading: false,
          })
          throw error
        }
      },

      resetPassword: async (token: string, newPassword: string) => {
        set({ isLoading: true, error: null })
        try {
          await account.updateRecovery(token, newPassword, newPassword)
          set({ isLoading: false })
        } catch (error: any) {
          set({
            error: error.message || 'Password reset failed',
            isLoading: false,
          })
          throw error
        }
      },

      verifyOTP: async (email: string, otp: string) => {
        set({ isLoading: true, error: null })
        try {
          // Implementation depends on your OTP verification system
          // This is a placeholder
          await new Promise(resolve => setTimeout(resolve, 1000))
          set({ isLoading: false })
        } catch (error: any) {
          set({
            error: error.message || 'OTP verification failed',
            isLoading: false,
          })
          throw error
        }
      },

      updateProfile: async (updates: Partial<User>) => {
        set({ isLoading: true, error: null })
        try {
          const { user } = get()
          if (!user) throw new Error('No user logged in')

          // Update name if provided
          if (updates.name) {
            await account.updateName(updates.name)
          }

          // Update email if provided
          if (updates.email) {
            await account.updateEmail(updates.email, user.email)
          }

          // Update phone if provided
          if (updates.phone) {
            await account.updatePhone(updates.phone, '')
          }

          const updatedUser = { ...user, ...updates }
          set({ user: updatedUser, isLoading: false })
        } catch (error: any) {
          set({
            error: error.message || 'Profile update failed',
            isLoading: false,
          })
          throw error
        }
      },

      addAddress: async (address: Omit<Address, 'id'>) => {
        const { user } = get()
        if (!user) throw new Error('No user logged in')

        const newAddress: Address = {
          ...address,
          id: `addr_${Date.now()}`,
        }

        const updatedUser = {
          ...user,
          addresses: [...user.addresses, newAddress],
        }

        set({ user: updatedUser })
      },

      updateAddress: async (addressId: string, updates: Partial<Address>) => {
        const { user } = get()
        if (!user) throw new Error('No user logged in')

        const updatedAddresses = user.addresses.map(addr =>
          addr.id === addressId ? { ...addr, ...updates } : addr
        )

        set({
          user: {
            ...user,
            addresses: updatedAddresses,
          },
        })
      },

      deleteAddress: async (addressId: string) => {
        const { user } = get()
        if (!user) throw new Error('No user logged in')

        const updatedAddresses = user.addresses.filter(addr => addr.id !== addressId)

        set({
          user: {
            ...user,
            addresses: updatedAddresses,
          },
        })
      },

      setDefaultAddress: async (addressId: string) => {
        const { user } = get()
        if (!user) throw new Error('No user logged in')

        const updatedAddresses = user.addresses.map(addr => ({
          ...addr,
          isDefault: addr.id === addressId,
        }))

        set({
          user: {
            ...user,
            addresses: updatedAddresses,
          },
        })
      },

      updatePreferences: async (preferences: Partial<UserPreferences>) => {
        const { user } = get()
        if (!user) throw new Error('No user logged in')

        const updatedPreferences = {
          ...user.preferences,
          ...preferences,
        }

        set({
          user: {
            ...user,
            preferences: updatedPreferences,
          },
        })
      },

      completeOnboarding: () => {
        set({ hasCompletedOnboarding: true })
      },

      clearError: () => {
        set({ error: null })
      },

      refreshUser: async () => {
        set({ isLoading: true })
        try {
          const user = await account.get()
          
          const { user: currentUser } = get()
          if (currentUser) {
            set({
              user: {
                ...currentUser,
                email: user.email,
                name: user.name,
                phone: user.phone,
                updatedAt: user.$updatedAt,
              },
              isLoading: false,
            })
          }
        } catch (error: any) {
          set({
            error: error.message || 'Failed to refresh user data',
            isLoading: false,
          })
        }
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        hasCompletedOnboarding: state.hasCompletedOnboarding,
      }),
    }
  )
)
