export default {
  expo: {
    name: "<PERSON><PERSON><PERSON><PERSON><PERSON>+ Runner",
    slug: "hvppyplug-runner",
    version: "1.0.0",
    orientation: "portrait",
    icon: "./assets/icon.png",
    userInterfaceStyle: "automatic",
    newArchEnabled: true,
    splash: {
      image: "./assets/splash-icon.png",
      resizeMode: "contain",
      backgroundColor: "#FF6B35"
    },
    assetBundlePatterns: [
      "**/*"
    ],
    ios: {
      supportsTablet: false,
      bundleIdentifier: "com.hvppyplug.runner",
      infoPlist: {
        NSLocationWhenInUseUsageDescription: "This app needs location access to track deliveries and provide accurate delivery updates to customers.",
        NSLocationAlwaysAndWhenInUseUsageDescription: "This app needs background location access to track deliveries even when the app is not active.",
        NSCameraUsageDescription: "This app needs camera access to take photos for delivery confirmation.",
        NSMicrophoneUsageDescription: "This app needs microphone access for voice notes and customer communication.",
        UIBackgroundModes: ["location", "background-fetch", "background-processing"]
      }
    },
    android: {
      adaptiveIcon: {
        foregroundImage: "./assets/adaptive-icon.png",
        backgroundColor: "#FF6B35"
      },
      package: "com.hvppyplug.runner",
      edgeToEdgeEnabled: true,
      permissions: [
        "ACCESS_FINE_LOCATION",
        "ACCESS_COARSE_LOCATION",
        "ACCESS_BACKGROUND_LOCATION",
        "CAMERA",
        "RECORD_AUDIO",
        "RECEIVE_BOOT_COMPLETED",
        "VIBRATE",
        "WAKE_LOCK"
      ]
    },
    web: {
      favicon: "./assets/favicon.png"
    },
    plugins: [
      "expo-location",
      "expo-camera",
      "expo-notifications",
      [
        "expo-task-manager",
        {
          backgroundLocationTask: "background-location-task"
        }
      ],
      [
        "expo-background-fetch",
        {
          backgroundFetchTask: "background-fetch-task"
        }
      ]
    ],
    extra: {
      // Appwrite Configuration
      appwriteEndpoint:
        process.env.EXPO_PUBLIC_APPWRITE_ENDPOINT ||
        "https://cloud.appwrite.io/v1",
      appwriteProjectId: process.env.EXPO_PUBLIC_APPWRITE_PROJECT_ID || "",
      appwriteDatabaseId:
        process.env.EXPO_PUBLIC_APPWRITE_DATABASE_ID || "hvppyplug-main",
      appwriteStorageId:
        process.env.EXPO_PUBLIC_APPWRITE_STORAGE_ID || "images",

      // Environment
      environment: process.env.NODE_ENV || "development",

      // Feature Flags
      enablePushNotifications:
        process.env.EXPO_PUBLIC_ENABLE_PUSH_NOTIFICATIONS === "true",
      enableLocationTracking:
        process.env.EXPO_PUBLIC_ENABLE_LOCATION_TRACKING === "true",
      enableOfflineMode: process.env.EXPO_PUBLIC_ENABLE_OFFLINE_MODE === "true",
    }
  }
};
