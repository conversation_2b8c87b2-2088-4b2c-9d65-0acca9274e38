packages:
  - 'packages/*'
  - 'apps/*'
  - 'backend'

# Catalog for version pinning across workspace
# This ensures consistent React versions across all apps
catalog:
  react: "19.0.0"
  react-dom: "19.0.0"
  react-native: "0.79.5"
  expo: "~53.0.20"
  typescript: "~5.8.3"
  "@types/react": "~19.0.10"
  "@babel/core": "^7.25.2"

# Shared dependencies that should be consistent
sharedWorkspaceLockfile: true
